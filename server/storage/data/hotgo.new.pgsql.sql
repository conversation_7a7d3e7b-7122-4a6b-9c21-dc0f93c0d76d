/*
 PostgreSQL Data Transfer
 Converted from MySQL to PostgreSQL

 Source Server Type    : MySQL
 Target Server Type    : PostgreSQL
 File Encoding         : UTF8

 Date: 12/06/2025 17:12:48
*/

-- PostgreSQL specific settings
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

-- ----------------------------
-- Table structure for hg_addon_hgexample_table
-- ----------------------------
DROP TABLE IF EXISTS hg_addon_hgexample_table CASCADE;
CREATE TABLE hg_addon_hgexample_table (
  id BIGSERIAL PRIMARY KEY,
  pid BIGINT NOT NULL,
  level INTEGER DEFAULT 1,
  tree VARCHAR(512) DEFAULT NULL,
  category_id BIGINT DEFAULT NULL,
  flag JSONB DEFAULT NULL,
  title VARCHAR(255) NOT NULL,
  description VARCHAR(255) DEFAULT NULL,
  content TEXT,
  image VARCHAR(255) DEFAULT NULL,
  images J<PERSON>N<PERSON> DEFAULT NULL,
  attachfile VARCHAR(255) DEFAULT NULL,
  attachfiles JSONB DEFAULT NULL,
  map JSONB DEFAULT NULL,
  star DECIMAL(5,1) DEFAULT 0.0,
  price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  views BIGINT DEFAULT NULL,
  activity_at DATE DEFAULT NULL,
  start_at TIMESTAMP DEFAULT NULL,
  end_at TIMESTAMP DEFAULT NULL,
  switch BOOLEAN DEFAULT NULL,
  sort INTEGER DEFAULT NULL,
  avatar VARCHAR(255) DEFAULT '',
  sex BOOLEAN DEFAULT NULL,
  qq VARCHAR(20) DEFAULT '',
  email VARCHAR(60) DEFAULT '',
  mobile VARCHAR(20) DEFAULT '',
  hobby JSONB DEFAULT NULL,
  channel INTEGER DEFAULT 1,
  city_id BIGINT DEFAULT 0,
  remark VARCHAR(255) DEFAULT NULL,
  status BOOLEAN DEFAULT TRUE,
  created_by BIGINT DEFAULT 0,
  updated_by BIGINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_addon_hgexample_table IS '插件_案例_表格';
COMMENT ON COLUMN hg_addon_hgexample_table.id IS 'ID';
COMMENT ON COLUMN hg_addon_hgexample_table.pid IS '上级ID';
COMMENT ON COLUMN hg_addon_hgexample_table.level IS '树等级';
COMMENT ON COLUMN hg_addon_hgexample_table.tree IS '关系树';
COMMENT ON COLUMN hg_addon_hgexample_table.category_id IS '分类ID';
COMMENT ON COLUMN hg_addon_hgexample_table.flag IS '标签';
COMMENT ON COLUMN hg_addon_hgexample_table.title IS '标题';
COMMENT ON COLUMN hg_addon_hgexample_table.description IS '描述';
COMMENT ON COLUMN hg_addon_hgexample_table.content IS '内容';
COMMENT ON COLUMN hg_addon_hgexample_table.image IS '单图';
COMMENT ON COLUMN hg_addon_hgexample_table.images IS '多图';
COMMENT ON COLUMN hg_addon_hgexample_table.attachfile IS '附件';
COMMENT ON COLUMN hg_addon_hgexample_table.attachfiles IS '多附件';
COMMENT ON COLUMN hg_addon_hgexample_table.map IS '动态键值对';
COMMENT ON COLUMN hg_addon_hgexample_table.star IS '推荐星';
COMMENT ON COLUMN hg_addon_hgexample_table.price IS '价格';
COMMENT ON COLUMN hg_addon_hgexample_table.views IS '浏览次数';
COMMENT ON COLUMN hg_addon_hgexample_table.activity_at IS '活动时间';
COMMENT ON COLUMN hg_addon_hgexample_table.start_at IS '开启时间';
COMMENT ON COLUMN hg_addon_hgexample_table.end_at IS '结束时间';
COMMENT ON COLUMN hg_addon_hgexample_table.switch IS '开关';
COMMENT ON COLUMN hg_addon_hgexample_table.sort IS '排序';
COMMENT ON COLUMN hg_addon_hgexample_table.avatar IS '头像';
COMMENT ON COLUMN hg_addon_hgexample_table.sex IS '性别';
COMMENT ON COLUMN hg_addon_hgexample_table.qq IS 'qq';
COMMENT ON COLUMN hg_addon_hgexample_table.email IS '邮箱';
COMMENT ON COLUMN hg_addon_hgexample_table.mobile IS '手机号码';
COMMENT ON COLUMN hg_addon_hgexample_table.hobby IS '爱好';
COMMENT ON COLUMN hg_addon_hgexample_table.channel IS '渠道';
COMMENT ON COLUMN hg_addon_hgexample_table.city_id IS '所在城市';
COMMENT ON COLUMN hg_addon_hgexample_table.remark IS '备注';
COMMENT ON COLUMN hg_addon_hgexample_table.status IS '状态';
COMMENT ON COLUMN hg_addon_hgexample_table.created_by IS '创建者';
COMMENT ON COLUMN hg_addon_hgexample_table.updated_by IS '更新者';
COMMENT ON COLUMN hg_addon_hgexample_table.created_at IS '创建时间';
COMMENT ON COLUMN hg_addon_hgexample_table.updated_at IS '修改时间';
COMMENT ON COLUMN hg_addon_hgexample_table.deleted_at IS '删除时间';

-- ----------------------------
-- Table structure for hg_addon_hgexample_tenant_order
-- ----------------------------
DROP TABLE IF EXISTS hg_addon_hgexample_tenant_order CASCADE;
CREATE TABLE hg_addon_hgexample_tenant_order (
  id BIGSERIAL PRIMARY KEY,
  tenant_id BIGINT DEFAULT NULL,
  merchant_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  product_name VARCHAR(255) DEFAULT NULL,
  order_sn VARCHAR(64) DEFAULT NULL,
  money DECIMAL(10,2) NOT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  status SMALLINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_addon_hgexample_tenant_order IS '多租户_充值订单';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.id IS '主键';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.tenant_id IS '租户ID';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.merchant_id IS '商户ID';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.user_id IS '用户ID';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.product_name IS '购买产品';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.order_sn IS '订单号';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.money IS '充值金额';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.remark IS '备注';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.status IS '订单状态';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.created_at IS '创建时间';
COMMENT ON COLUMN hg_addon_hgexample_tenant_order.updated_at IS '修改时间';

-- Create indexes for hg_addon_hgexample_tenant_order
CREATE INDEX idx_hg_addon_hgexample_tenant_order_order_sn ON hg_addon_hgexample_tenant_order(order_sn);
CREATE INDEX idx_hg_addon_hgexample_tenant_order_user_id ON hg_addon_hgexample_tenant_order(user_id);
CREATE INDEX idx_hg_addon_hgexample_tenant_order_merchant_id ON hg_addon_hgexample_tenant_order(merchant_id);
CREATE INDEX idx_hg_addon_hgexample_tenant_order_tenant_id ON hg_addon_hgexample_tenant_order(tenant_id);

-- ----------------------------
-- Table structure for hg_admin_cash
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_cash CASCADE;
CREATE TABLE hg_admin_cash (
  id BIGSERIAL PRIMARY KEY,
  member_id BIGINT NOT NULL,
  money DECIMAL(10,2) NOT NULL,
  fee DECIMAL(10,2) NOT NULL,
  last_money DECIMAL(10,2) NOT NULL,
  ip VARCHAR(128) NOT NULL,
  status BIGINT NOT NULL,
  msg VARCHAR(128) NOT NULL,
  handle_at TIMESTAMP DEFAULT NULL,
  created_at TIMESTAMP NOT NULL
);
COMMENT ON TABLE hg_admin_cash IS '管理员_提现记录表';
COMMENT ON COLUMN hg_admin_cash.id IS 'ID';
COMMENT ON COLUMN hg_admin_cash.member_id IS '管理员ID';
COMMENT ON COLUMN hg_admin_cash.money IS '提现金额';
COMMENT ON COLUMN hg_admin_cash.fee IS '手续费';
COMMENT ON COLUMN hg_admin_cash.last_money IS '最终到账金额';
COMMENT ON COLUMN hg_admin_cash.ip IS '申请人IP';
COMMENT ON COLUMN hg_admin_cash.status IS '状态码';
COMMENT ON COLUMN hg_admin_cash.msg IS '处理结果';
COMMENT ON COLUMN hg_admin_cash.handle_at IS '处理时间';
COMMENT ON COLUMN hg_admin_cash.created_at IS '申请时间';

-- Create indexes for hg_admin_cash
CREATE INDEX idx_hg_admin_cash_member_id ON hg_admin_cash(member_id);

-- ----------------------------
-- Table structure for hg_admin_credits_log
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_credits_log CASCADE;
CREATE TABLE hg_admin_credits_log (
  id BIGSERIAL PRIMARY KEY,
  member_id BIGINT DEFAULT 0,
  app_id VARCHAR(64) DEFAULT NULL,
  addons_name VARCHAR(100) NOT NULL DEFAULT '',
  credit_type VARCHAR(32) NOT NULL DEFAULT '',
  credit_group VARCHAR(32) DEFAULT NULL,
  before_num DECIMAL(10,2) DEFAULT 0.00,
  num DECIMAL(10,2) DEFAULT 0.00,
  after_num DECIMAL(10,2) DEFAULT 0.00,
  remark VARCHAR(255) DEFAULT NULL,
  ip VARCHAR(20) DEFAULT NULL,
  map_id BIGINT DEFAULT 0,
  status BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_credits_log IS '管理员_资产变动表';
COMMENT ON COLUMN hg_admin_credits_log.id IS '变动ID';
COMMENT ON COLUMN hg_admin_credits_log.member_id IS '管理员ID';
COMMENT ON COLUMN hg_admin_credits_log.app_id IS '应用id';
COMMENT ON COLUMN hg_admin_credits_log.addons_name IS '插件名称';
COMMENT ON COLUMN hg_admin_credits_log.credit_type IS '变动类型';
COMMENT ON COLUMN hg_admin_credits_log.credit_group IS '变动组别';
COMMENT ON COLUMN hg_admin_credits_log.before_num IS '变动前';
COMMENT ON COLUMN hg_admin_credits_log.num IS '变动数据';
COMMENT ON COLUMN hg_admin_credits_log.after_num IS '变动后';
COMMENT ON COLUMN hg_admin_credits_log.remark IS '备注';
COMMENT ON COLUMN hg_admin_credits_log.ip IS '操作人IP';
COMMENT ON COLUMN hg_admin_credits_log.map_id IS '关联ID';
COMMENT ON COLUMN hg_admin_credits_log.status IS '状态';
COMMENT ON COLUMN hg_admin_credits_log.created_at IS '创建时间';
COMMENT ON COLUMN hg_admin_credits_log.updated_at IS '修改时间';

-- Create indexes for hg_admin_credits_log
CREATE INDEX idx_hg_admin_credits_log_member_id ON hg_admin_credits_log(member_id);

-- ----------------------------
-- Table structure for hg_admin_dept
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_dept CASCADE;
CREATE TABLE hg_admin_dept (
  id BIGSERIAL PRIMARY KEY,
  pid BIGINT DEFAULT 0,
  name VARCHAR(32) DEFAULT NULL,
  code VARCHAR(255) DEFAULT NULL,
  type VARCHAR(10) DEFAULT NULL,
  leader VARCHAR(32) DEFAULT NULL,
  phone VARCHAR(11) DEFAULT NULL,
  email VARCHAR(64) DEFAULT NULL,
  level INTEGER NOT NULL,
  tree VARCHAR(512) DEFAULT NULL,
  sort INTEGER DEFAULT 0,
  status BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_dept IS '管理员_部门';
COMMENT ON COLUMN hg_admin_dept.id IS '部门ID';
COMMENT ON COLUMN hg_admin_dept.pid IS '父部门ID';
COMMENT ON COLUMN hg_admin_dept.name IS '部门名称';
COMMENT ON COLUMN hg_admin_dept.code IS '部门编码';
COMMENT ON COLUMN hg_admin_dept.type IS '部门类型';
COMMENT ON COLUMN hg_admin_dept.leader IS '负责人';
COMMENT ON COLUMN hg_admin_dept.phone IS '联系电话';
COMMENT ON COLUMN hg_admin_dept.email IS '邮箱';
COMMENT ON COLUMN hg_admin_dept.level IS '关系树等级';
COMMENT ON COLUMN hg_admin_dept.tree IS '关系树';
COMMENT ON COLUMN hg_admin_dept.sort IS '排序';
COMMENT ON COLUMN hg_admin_dept.status IS '部门状态';
COMMENT ON COLUMN hg_admin_dept.created_at IS '创建时间';
COMMENT ON COLUMN hg_admin_dept.updated_at IS '更新时间';

-- Create indexes for hg_admin_dept
CREATE INDEX idx_hg_admin_dept_pid ON hg_admin_dept(pid);

-- ----------------------------
-- Table structure for hg_admin_member
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_member CASCADE;
CREATE TABLE hg_admin_member (
  id BIGSERIAL PRIMARY KEY,
  dept_id BIGINT DEFAULT 0,
  role_id BIGINT DEFAULT 10,
  real_name VARCHAR(32) DEFAULT '',
  username VARCHAR(20) NOT NULL DEFAULT '',
  password_hash CHAR(32) NOT NULL DEFAULT '',
  salt CHAR(16) NOT NULL,
  password_reset_token VARCHAR(150) DEFAULT '',
  integral DECIMAL(10,2) DEFAULT 0.00,
  balance DECIMAL(10,2) DEFAULT 0.00,
  avatar CHAR(150) DEFAULT '',
  sex BOOLEAN DEFAULT TRUE,
  qq VARCHAR(20) DEFAULT '',
  email VARCHAR(60) DEFAULT '',
  mobile VARCHAR(20) DEFAULT '',
  birthday DATE DEFAULT NULL,
  city_id BIGINT DEFAULT 0,
  address VARCHAR(100) DEFAULT '',
  pid BIGINT NOT NULL,
  level INTEGER DEFAULT 1,
  tree VARCHAR(512) NOT NULL,
  invite_code VARCHAR(12) DEFAULT NULL,
  cash JSONB DEFAULT NULL,
  last_active_at TIMESTAMP DEFAULT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  status BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_member IS '管理员_用户表';
COMMENT ON COLUMN hg_admin_member.id IS '管理员ID';
COMMENT ON COLUMN hg_admin_member.dept_id IS '部门ID';
COMMENT ON COLUMN hg_admin_member.role_id IS '角色ID';
COMMENT ON COLUMN hg_admin_member.real_name IS '真实姓名';
COMMENT ON COLUMN hg_admin_member.username IS '帐号';
COMMENT ON COLUMN hg_admin_member.password_hash IS '密码';
COMMENT ON COLUMN hg_admin_member.salt IS '密码盐';
COMMENT ON COLUMN hg_admin_member.password_reset_token IS '密码重置令牌';
COMMENT ON COLUMN hg_admin_member.integral IS '积分';
COMMENT ON COLUMN hg_admin_member.balance IS '余额';
COMMENT ON COLUMN hg_admin_member.avatar IS '头像';
COMMENT ON COLUMN hg_admin_member.sex IS '性别';
COMMENT ON COLUMN hg_admin_member.qq IS 'qq';
COMMENT ON COLUMN hg_admin_member.email IS '邮箱';
COMMENT ON COLUMN hg_admin_member.mobile IS '手机号码';
COMMENT ON COLUMN hg_admin_member.birthday IS '生日';
COMMENT ON COLUMN hg_admin_member.city_id IS '城市编码';
COMMENT ON COLUMN hg_admin_member.address IS '联系地址';
COMMENT ON COLUMN hg_admin_member.pid IS '上级管理员ID';
COMMENT ON COLUMN hg_admin_member.level IS '关系树等级';
COMMENT ON COLUMN hg_admin_member.tree IS '关系树';
COMMENT ON COLUMN hg_admin_member.invite_code IS '邀请码';
COMMENT ON COLUMN hg_admin_member.cash IS '提现配置';
COMMENT ON COLUMN hg_admin_member.last_active_at IS '最后活跃时间';
COMMENT ON COLUMN hg_admin_member.remark IS '备注';
COMMENT ON COLUMN hg_admin_member.status IS '状态';
COMMENT ON COLUMN hg_admin_member.created_at IS '创建时间';
COMMENT ON COLUMN hg_admin_member.updated_at IS '修改时间';

-- Create indexes for hg_admin_member
CREATE UNIQUE INDEX idx_hg_admin_member_invite_code ON hg_admin_member(invite_code);
CREATE INDEX idx_hg_admin_member_dept_id ON hg_admin_member(dept_id);
CREATE INDEX idx_hg_admin_member_pid ON hg_admin_member(pid);

-- ----------------------------
-- Table structure for hg_admin_member_post
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_member_post CASCADE;
CREATE TABLE hg_admin_member_post (
  member_id BIGINT NOT NULL,
  post_id BIGINT NOT NULL,
  PRIMARY KEY (member_id, post_id)
);
COMMENT ON TABLE hg_admin_member_post IS '管理员_用户岗位关联';
COMMENT ON COLUMN hg_admin_member_post.member_id IS '管理员ID';
COMMENT ON COLUMN hg_admin_member_post.post_id IS '岗位ID';

-- ----------------------------
-- Table structure for hg_admin_member_role
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_member_role CASCADE;
CREATE TABLE hg_admin_member_role (
  member_id BIGINT NOT NULL,
  role_id BIGINT NOT NULL,
  PRIMARY KEY (member_id, role_id)
);
COMMENT ON TABLE hg_admin_member_role IS '管理员_用户角色关联';
COMMENT ON COLUMN hg_admin_member_role.member_id IS '管理员ID';
COMMENT ON COLUMN hg_admin_member_role.role_id IS '角色ID';

-- ----------------------------
-- Table structure for hg_admin_menu
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_menu CASCADE;
CREATE TABLE hg_admin_menu (
  id BIGSERIAL PRIMARY KEY,
  pid BIGINT DEFAULT 0,
  level INTEGER NOT NULL DEFAULT 1,
  tree VARCHAR(255) NOT NULL,
  title VARCHAR(64) NOT NULL,
  name VARCHAR(128) NOT NULL,
  path VARCHAR(200) DEFAULT NULL,
  icon VARCHAR(128) DEFAULT NULL,
  type SMALLINT NOT NULL DEFAULT 1,
  redirect VARCHAR(255) DEFAULT NULL,
  permissions VARCHAR(512) DEFAULT NULL,
  permission_name VARCHAR(64) DEFAULT NULL,
  component VARCHAR(255) NOT NULL,
  always_show BOOLEAN DEFAULT FALSE,
  active_menu VARCHAR(255) DEFAULT NULL,
  is_root BOOLEAN DEFAULT FALSE,
  is_frame BOOLEAN DEFAULT TRUE,
  frame_src VARCHAR(512) DEFAULT NULL,
  keep_alive BOOLEAN DEFAULT FALSE,
  hidden BOOLEAN DEFAULT FALSE,
  affix BOOLEAN DEFAULT FALSE,
  sort INTEGER DEFAULT 0,
  remark VARCHAR(255) DEFAULT NULL,
  status BOOLEAN DEFAULT TRUE,
  updated_at TIMESTAMP DEFAULT NULL,
  created_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_menu IS '管理员_菜单权限';
COMMENT ON COLUMN hg_admin_menu.id IS '菜单ID';
COMMENT ON COLUMN hg_admin_menu.pid IS '父菜单ID';
COMMENT ON COLUMN hg_admin_menu.level IS '关系树等级';
COMMENT ON COLUMN hg_admin_menu.tree IS '关系树';
COMMENT ON COLUMN hg_admin_menu.title IS '菜单名称';
COMMENT ON COLUMN hg_admin_menu.name IS '名称编码';
COMMENT ON COLUMN hg_admin_menu.path IS '路由地址';
COMMENT ON COLUMN hg_admin_menu.icon IS '菜单图标';
COMMENT ON COLUMN hg_admin_menu.type IS '菜单类型（1目录 2菜单 3按钮）';
COMMENT ON COLUMN hg_admin_menu.redirect IS '重定向地址';
COMMENT ON COLUMN hg_admin_menu.permissions IS '菜单包含权限集合';
COMMENT ON COLUMN hg_admin_menu.permission_name IS '权限名称';
COMMENT ON COLUMN hg_admin_menu.component IS '组件路径';
COMMENT ON COLUMN hg_admin_menu.always_show IS '取消自动计算根路由模式';
COMMENT ON COLUMN hg_admin_menu.active_menu IS '高亮菜单编码';
COMMENT ON COLUMN hg_admin_menu.is_root IS '是否跟路由';
COMMENT ON COLUMN hg_admin_menu.is_frame IS '是否内嵌';
COMMENT ON COLUMN hg_admin_menu.frame_src IS '内联外部地址';
COMMENT ON COLUMN hg_admin_menu.keep_alive IS '缓存该路由';
COMMENT ON COLUMN hg_admin_menu.hidden IS '是否隐藏';
COMMENT ON COLUMN hg_admin_menu.affix IS '是否固定';
COMMENT ON COLUMN hg_admin_menu.sort IS '排序';
COMMENT ON COLUMN hg_admin_menu.remark IS '备注';
COMMENT ON COLUMN hg_admin_menu.status IS '菜单状态';
COMMENT ON COLUMN hg_admin_menu.updated_at IS '更新时间';
COMMENT ON COLUMN hg_admin_menu.created_at IS '创建时间';

-- Create indexes for hg_admin_menu
CREATE UNIQUE INDEX idx_hg_admin_menu_name ON hg_admin_menu(name);
CREATE INDEX idx_hg_admin_menu_pid ON hg_admin_menu(pid);
CREATE INDEX idx_hg_admin_menu_status ON hg_admin_menu(status);
CREATE INDEX idx_hg_admin_menu_type ON hg_admin_menu(type);

-- ----------------------------
-- Table structure for hg_admin_notice
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_notice CASCADE;
CREATE TABLE hg_admin_notice (
  id BIGSERIAL PRIMARY KEY,
  title VARCHAR(64) NOT NULL,
  type BIGINT NOT NULL,
  tag INTEGER DEFAULT NULL,
  content TEXT NOT NULL,
  receiver JSONB DEFAULT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  sort INTEGER NOT NULL DEFAULT 0,
  status BOOLEAN DEFAULT TRUE,
  created_by BIGINT DEFAULT NULL,
  updated_by BIGINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL,
  deleted_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_notice IS '管理员_通知公告';
COMMENT ON COLUMN hg_admin_notice.id IS '公告ID';
COMMENT ON COLUMN hg_admin_notice.title IS '公告标题';
COMMENT ON COLUMN hg_admin_notice.type IS '公告类型';
COMMENT ON COLUMN hg_admin_notice.tag IS '标签';
COMMENT ON COLUMN hg_admin_notice.content IS '公告内容';
COMMENT ON COLUMN hg_admin_notice.receiver IS '接收者';
COMMENT ON COLUMN hg_admin_notice.remark IS '备注';
COMMENT ON COLUMN hg_admin_notice.sort IS '排序';
COMMENT ON COLUMN hg_admin_notice.status IS '公告状态';
COMMENT ON COLUMN hg_admin_notice.created_by IS '发送人';
COMMENT ON COLUMN hg_admin_notice.updated_by IS '修改人';
COMMENT ON COLUMN hg_admin_notice.created_at IS '创建时间';
COMMENT ON COLUMN hg_admin_notice.updated_at IS '更新时间';
COMMENT ON COLUMN hg_admin_notice.deleted_at IS '删除时间';

-- ----------------------------
-- Table structure for hg_admin_notice_read
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_notice_read CASCADE;
CREATE TABLE hg_admin_notice_read (
  id BIGSERIAL PRIMARY KEY,
  notice_id BIGINT NOT NULL,
  member_id BIGINT NOT NULL,
  clicks INTEGER DEFAULT 1,
  updated_at TIMESTAMP DEFAULT NULL,
  created_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_notice_read IS '管理员_公告已读记录';
COMMENT ON COLUMN hg_admin_notice_read.id IS '记录ID';
COMMENT ON COLUMN hg_admin_notice_read.notice_id IS '公告ID';
COMMENT ON COLUMN hg_admin_notice_read.member_id IS '会员ID';
COMMENT ON COLUMN hg_admin_notice_read.clicks IS '已读次数';
COMMENT ON COLUMN hg_admin_notice_read.updated_at IS '更新时间';
COMMENT ON COLUMN hg_admin_notice_read.created_at IS '阅读时间';

-- Create indexes for hg_admin_notice_read
CREATE UNIQUE INDEX idx_hg_admin_notice_read_notice_member ON hg_admin_notice_read(notice_id, member_id);

-- ----------------------------
-- Table structure for hg_admin_oauth
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_oauth CASCADE;
CREATE TABLE hg_admin_oauth (
  id BIGSERIAL PRIMARY KEY,
  member_id BIGINT DEFAULT 0,
  unionid VARCHAR(64) DEFAULT '',
  oauth_client VARCHAR(32) DEFAULT NULL,
  oauth_openid VARCHAR(128) DEFAULT NULL,
  sex BOOLEAN DEFAULT TRUE,
  nickname VARCHAR(255) DEFAULT NULL,
  head_portrait VARCHAR(512) DEFAULT NULL,
  birthday DATE DEFAULT NULL,
  country VARCHAR(100) DEFAULT '',
  province VARCHAR(100) DEFAULT '',
  city VARCHAR(100) DEFAULT '',
  status BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_oauth IS '管理员_第三方登录';
COMMENT ON COLUMN hg_admin_oauth.id IS '主键';
COMMENT ON COLUMN hg_admin_oauth.member_id IS '用户ID';
COMMENT ON COLUMN hg_admin_oauth.unionid IS '唯一ID';
COMMENT ON COLUMN hg_admin_oauth.oauth_client IS '授权组别';
COMMENT ON COLUMN hg_admin_oauth.oauth_openid IS '授权开放ID';
COMMENT ON COLUMN hg_admin_oauth.sex IS '性别';
COMMENT ON COLUMN hg_admin_oauth.nickname IS '昵称';
COMMENT ON COLUMN hg_admin_oauth.head_portrait IS '头像';
COMMENT ON COLUMN hg_admin_oauth.birthday IS '生日';
COMMENT ON COLUMN hg_admin_oauth.country IS '国家';
COMMENT ON COLUMN hg_admin_oauth.province IS '省';
COMMENT ON COLUMN hg_admin_oauth.city IS '市';
COMMENT ON COLUMN hg_admin_oauth.status IS '状态';
COMMENT ON COLUMN hg_admin_oauth.created_at IS '创建时间';
COMMENT ON COLUMN hg_admin_oauth.updated_at IS '修改时间';

-- Create indexes for hg_admin_oauth
CREATE INDEX idx_hg_admin_oauth_client_openid ON hg_admin_oauth(oauth_client, oauth_openid);
CREATE INDEX idx_hg_admin_oauth_member_id ON hg_admin_oauth(member_id);

-- ----------------------------
-- Table structure for hg_admin_order
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_order CASCADE;
CREATE TABLE hg_admin_order (
  id BIGSERIAL PRIMARY KEY,
  member_id BIGINT DEFAULT 0,
  order_type VARCHAR(32) NOT NULL,
  product_id BIGINT DEFAULT NULL,
  order_sn VARCHAR(64) DEFAULT '',
  money DECIMAL(10,2) NOT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  refund_reason VARCHAR(255) DEFAULT NULL,
  reject_refund_reason VARCHAR(255) DEFAULT NULL,
  status SMALLINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_order IS '管理员_充值订单';
COMMENT ON COLUMN hg_admin_order.id IS '主键';
COMMENT ON COLUMN hg_admin_order.member_id IS '管理员id';
COMMENT ON COLUMN hg_admin_order.order_type IS '订单类型';
COMMENT ON COLUMN hg_admin_order.product_id IS '产品id';
COMMENT ON COLUMN hg_admin_order.order_sn IS '关联订单号';
COMMENT ON COLUMN hg_admin_order.money IS '充值金额';
COMMENT ON COLUMN hg_admin_order.remark IS '备注';
COMMENT ON COLUMN hg_admin_order.refund_reason IS '退款原因';
COMMENT ON COLUMN hg_admin_order.reject_refund_reason IS '拒绝退款原因';
COMMENT ON COLUMN hg_admin_order.status IS '状态';
COMMENT ON COLUMN hg_admin_order.created_at IS '创建时间';
COMMENT ON COLUMN hg_admin_order.updated_at IS '修改时间';

-- Create indexes for hg_admin_order
CREATE INDEX idx_hg_admin_order_order_sn ON hg_admin_order(order_sn);
CREATE INDEX idx_hg_admin_order_member_id ON hg_admin_order(member_id);

-- ----------------------------
-- Table structure for hg_admin_post
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_post CASCADE;
CREATE TABLE hg_admin_post (
  id BIGSERIAL PRIMARY KEY,
  code VARCHAR(64) NOT NULL,
  name VARCHAR(50) NOT NULL,
  remark VARCHAR(500) DEFAULT NULL,
  sort INTEGER NOT NULL,
  status BOOLEAN NOT NULL,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_post IS '管理员_岗位';
COMMENT ON COLUMN hg_admin_post.id IS '岗位ID';
COMMENT ON COLUMN hg_admin_post.code IS '岗位编码';
COMMENT ON COLUMN hg_admin_post.name IS '岗位名称';
COMMENT ON COLUMN hg_admin_post.remark IS '备注';
COMMENT ON COLUMN hg_admin_post.sort IS '排序';
COMMENT ON COLUMN hg_admin_post.status IS '状态';
COMMENT ON COLUMN hg_admin_post.created_at IS '创建时间';
COMMENT ON COLUMN hg_admin_post.updated_at IS '更新时间';

-- ----------------------------
-- Table structure for hg_admin_role
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_role CASCADE;
CREATE TABLE hg_admin_role (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(32) NOT NULL,
  key VARCHAR(128) NOT NULL,
  data_scope SMALLINT DEFAULT 1,
  custom_dept JSONB DEFAULT NULL,
  pid BIGINT DEFAULT 0,
  level INTEGER NOT NULL DEFAULT 1,
  tree VARCHAR(512) DEFAULT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  sort INTEGER NOT NULL DEFAULT 0,
  status BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_admin_role IS '管理员_角色信息';
COMMENT ON COLUMN hg_admin_role.id IS '角色ID';
COMMENT ON COLUMN hg_admin_role.name IS '角色名称';
COMMENT ON COLUMN hg_admin_role.key IS '角色权限字符串';
COMMENT ON COLUMN hg_admin_role.data_scope IS '数据范围';
COMMENT ON COLUMN hg_admin_role.custom_dept IS '自定义部门权限';
COMMENT ON COLUMN hg_admin_role.pid IS '上级角色ID';
COMMENT ON COLUMN hg_admin_role.level IS '关系树等级';
COMMENT ON COLUMN hg_admin_role.tree IS '关系树';
COMMENT ON COLUMN hg_admin_role.remark IS '备注';
COMMENT ON COLUMN hg_admin_role.sort IS '排序';
COMMENT ON COLUMN hg_admin_role.status IS '角色状态';
COMMENT ON COLUMN hg_admin_role.created_at IS '创建时间';
COMMENT ON COLUMN hg_admin_role.updated_at IS '更新时间';

-- ----------------------------
-- Table structure for hg_admin_role_casbin
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_role_casbin CASCADE;
CREATE TABLE hg_admin_role_casbin (
  id BIGSERIAL PRIMARY KEY,
  p_type VARCHAR(64) DEFAULT NULL,
  v0 VARCHAR(256) DEFAULT NULL,
  v1 VARCHAR(256) DEFAULT NULL,
  v2 VARCHAR(256) DEFAULT NULL,
  v3 VARCHAR(256) DEFAULT NULL,
  v4 VARCHAR(256) DEFAULT NULL,
  v5 VARCHAR(256) DEFAULT NULL
);
COMMENT ON TABLE hg_admin_role_casbin IS '管理员_casbin权限表';

-- ----------------------------
-- Table structure for hg_admin_role_menu
-- ----------------------------
DROP TABLE IF EXISTS hg_admin_role_menu CASCADE;
CREATE TABLE hg_admin_role_menu (
  role_id BIGINT NOT NULL,
  menu_id BIGINT NOT NULL,
  PRIMARY KEY (role_id, menu_id)
);
COMMENT ON TABLE hg_admin_role_menu IS '管理员_角色菜单关联';
COMMENT ON COLUMN hg_admin_role_menu.role_id IS '角色ID';
COMMENT ON COLUMN hg_admin_role_menu.menu_id IS '菜单ID';

-- ----------------------------
-- Table structure for hg_pay_log
-- ----------------------------
DROP TABLE IF EXISTS hg_pay_log CASCADE;
CREATE TABLE hg_pay_log (
  id BIGSERIAL PRIMARY KEY,
  member_id BIGINT DEFAULT 0,
  app_id VARCHAR(50) DEFAULT NULL,
  addons_name VARCHAR(100) DEFAULT '',
  order_sn VARCHAR(64) DEFAULT '',
  order_group VARCHAR(32) DEFAULT '',
  openid VARCHAR(50) DEFAULT '',
  mch_id VARCHAR(20) DEFAULT '',
  subject VARCHAR(255) DEFAULT NULL,
  detail JSONB DEFAULT NULL,
  auth_code VARCHAR(50) DEFAULT '',
  out_trade_no VARCHAR(128) DEFAULT '',
  transaction_id VARCHAR(128) DEFAULT NULL,
  pay_type VARCHAR(32) NOT NULL,
  pay_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  actual_amount DECIMAL(10,2) DEFAULT NULL,
  pay_status SMALLINT DEFAULT 0,
  pay_at TIMESTAMP DEFAULT NULL,
  trade_type VARCHAR(16) DEFAULT '',
  refund_sn VARCHAR(128) DEFAULT NULL,
  is_refund SMALLINT DEFAULT 0,
  custom TEXT,
  create_ip VARCHAR(128) DEFAULT NULL,
  pay_ip VARCHAR(128) DEFAULT NULL,
  notify_url VARCHAR(255) DEFAULT NULL,
  return_url VARCHAR(255) DEFAULT NULL,
  trace_ids JSONB DEFAULT NULL,
  status SMALLINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT NULL,
  updated_at TIMESTAMP DEFAULT NULL
);
COMMENT ON TABLE hg_pay_log IS '支付_支付日志';
COMMENT ON COLUMN hg_pay_log.id IS '主键';
COMMENT ON COLUMN hg_pay_log.member_id IS '会员ID';
COMMENT ON COLUMN hg_pay_log.app_id IS '应用ID';
COMMENT ON COLUMN hg_pay_log.addons_name IS '插件名称';
COMMENT ON COLUMN hg_pay_log.order_sn IS '关联订单号';
COMMENT ON COLUMN hg_pay_log.order_group IS '组别[默认统一支付类型]';
COMMENT ON COLUMN hg_pay_log.openid IS 'openid';
COMMENT ON COLUMN hg_pay_log.mch_id IS '商户支付账户';
COMMENT ON COLUMN hg_pay_log.subject IS '订单标题';
COMMENT ON COLUMN hg_pay_log.detail IS '支付商品详情';
COMMENT ON COLUMN hg_pay_log.auth_code IS '刷卡码';
COMMENT ON COLUMN hg_pay_log.out_trade_no IS '商户订单号';
COMMENT ON COLUMN hg_pay_log.transaction_id IS '交易号';
COMMENT ON COLUMN hg_pay_log.pay_type IS '支付类型';
COMMENT ON COLUMN hg_pay_log.pay_amount IS '支付金额';
COMMENT ON COLUMN hg_pay_log.actual_amount IS '实付金额';
COMMENT ON COLUMN hg_pay_log.pay_status IS '支付状态';
COMMENT ON COLUMN hg_pay_log.pay_at IS '支付时间';
COMMENT ON COLUMN hg_pay_log.trade_type IS '交易类型';
COMMENT ON COLUMN hg_pay_log.refund_sn IS '退款单号';
COMMENT ON COLUMN hg_pay_log.is_refund IS '是否退款 ';
COMMENT ON COLUMN hg_pay_log.custom IS '自定义参数';
COMMENT ON COLUMN hg_pay_log.create_ip IS '创建者IP';
COMMENT ON COLUMN hg_pay_log.pay_ip IS '支付者IP';
COMMENT ON COLUMN hg_pay_log.notify_url IS '支付通知回调地址';
COMMENT ON COLUMN hg_pay_log.return_url IS '买家付款成功跳转地址';
COMMENT ON COLUMN hg_pay_log.trace_ids IS '链路ID集合';
COMMENT ON COLUMN hg_pay_log.status IS '状态';
COMMENT ON COLUMN hg_pay_log.created_at IS '创建时间';
COMMENT ON COLUMN hg_pay_log.updated_at IS '修改时间';

-- Create indexes for hg_pay_log
CREATE UNIQUE INDEX idx_hg_pay_log_order_sn ON hg_pay_log(order_sn);
CREATE INDEX idx_hg_pay_log_member_id ON hg_pay_log(member_id);
