// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalTestCategoryDao is internal type for wrapping internal DAO implements.
type internalTestCategoryDao = *internal.TestCategoryDao

// testCategoryDao is the data access object for table hg_test_category.
// You can define custom methods on it to extend its functionality as you wish.
type testCategoryDao struct {
	internalTestCategoryDao
}

var (
	// TestCategory is globally public accessible object for table hg_test_category operations.
	TestCategory = testCategoryDao{
		internal.NewTestCategoryDao(),
	}
)

// Fill with you ideas below.
